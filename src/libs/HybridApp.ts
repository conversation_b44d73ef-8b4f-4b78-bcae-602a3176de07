import HybridAppModule from '@expensify/react-native-hybrid-app';
import Onyx from 'react-native-onyx';
import type {OnyxEntry} from 'react-native-onyx';
import CONFIG from '@src/CONFIG';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Credentials, HybridApp, Session, TryNewDot} from '@src/types/onyx';
import {isEmptyObject} from '@src/types/utils/EmptyObject';
import {closeReactNativeApp, setReadyToShowAuthScreens, setUseNewDotSignInPage} from './actions/HybridApp';
import {isAnonymousUser} from './actions/Session';
import Log from './Log';
import {getCurrentUserEmail} from './Network/NetworkStore';

let currentHybridApp: OnyxEntry<HybridApp>;
let currentTryNewDot: OnyxEntry<TryNewDot>;
let currentCredentials: OnyxEntry<Credentials>;
let currentSession: OnyxEntry<Session>;

Onyx.connect({
    key: ONYXKEYS.HYBRID_APP,
    callback: (hybridApp) => {
        currentHybridApp = hybridApp;
        handleChangeInHybridAppSignInFlow(hybridApp, currentTryNewDot, currentCredentials, currentSession);
    },
});

Onyx.connect({
    key: ONYXKEYS.NVP_TRY_NEW_DOT,
    callback: (tryNewDot) => {
        currentTryNewDot = tryNewDot;
        handleChangeInHybridAppSignInFlow(currentHybridApp, tryNewDot, currentCredentials, currentSession);
    },
});

Onyx.connect({
    key: ONYXKEYS.CREDENTIALS,
    callback: (credentials) => {
        currentCredentials = credentials;
        handleChangeInHybridAppSignInFlow(currentHybridApp, currentTryNewDot, credentials, currentSession);
    },
});

Onyx.connect({
    key: ONYXKEYS.SESSION,
    callback: (session: OnyxEntry<Session>) => {
        if (!currentSession?.authToken && session?.authToken) {
            handleChangeInHybridAppSignInFlow(currentHybridApp, currentTryNewDot, currentCredentials, session);
        } else if (isAnonymousUser(currentSession) && !isAnonymousUser(session)) {
            handleChangeInHybridAppSignInFlow(currentHybridApp, currentTryNewDot, currentCredentials, session, true);
        }
        currentSession = session;
    },
});

let activePolicyID: OnyxEntry<string>;
Onyx.connect({
    key: ONYXKEYS.NVP_ACTIVE_POLICY_ID,
    callback: (newActivePolicyID) => {
        activePolicyID = newActivePolicyID;
    },
});

function shouldUseOldApp(tryNewDot: TryNewDot) {
    if (isEmptyObject(tryNewDot) || isEmptyObject(tryNewDot.classicRedirect)) {
        return true;
    }
    return tryNewDot.classicRedirect.dismissed;
}

function handleChangeInHybridAppSignInFlow(
    hybridApp: OnyxEntry<HybridApp>,
    tryNewDot: OnyxEntry<TryNewDot>,
    credentials: OnyxEntry<Credentials>,
    session: OnyxEntry<Session>,
    usingSignInModal = false,
) {
    if (!CONFIG.IS_HYBRID_APP) {
        return;
    }

    if (!session?.authToken || (!hybridApp?.useNewDotSignInPage && !usingSignInModal)) {
        return;
    }

    if (isAnonymousUser()) {
        setUseNewDotSignInPage(false).then(() => {
            setReadyToShowAuthScreens(true);
        });
        return;
    }

    if (tryNewDot !== undefined && !!credentials?.autoGeneratedLogin && !!credentials?.autoGeneratedPassword) {
        // It's better to not pass function directly to Log.info to avoid bugs with evaluation
        const shouldUseOD = shouldUseOldApp(tryNewDot);
        Log.info(`[HybridApp] Performing sign-in${shouldUseOD ? '' : ' (in background)'} on OldDot side`);
        HybridAppModule.signInToOldDot({
            autoGeneratedLogin: credentials.autoGeneratedLogin,
            autoGeneratedPassword: credentials.autoGeneratedPassword,
            authToken: session.authToken,
            email: getCurrentUserEmail() ?? '',
            // eslint-disable-next-line rulesdir/no-default-id-values
            policyID: activePolicyID ?? '',
        });
        setUseNewDotSignInPage(false).then(() => {
            if (shouldUseOD) {
                closeReactNativeApp({shouldSignOut: false, shouldSetNVP: false});
            } else {
                Log.info('[HybridApp] The user should see NewDot. There is no need to block the user on the `SignInPage` until the sign-in process is completed on the OldDot side.');
                setReadyToShowAuthScreens(true);
            }
        });
    }
}
