diff --git a/node_modules/react-native/ReactAndroid/src/main/java/com/facebook/react/views/textinput/ReactEditText.java b/node_modules/react-native/ReactAndroid/src/main/java/com/facebook/react/views/textinput/ReactEditText.java
index 5a05e82..095313e 100644
--- a/node_modules/react-native/ReactAndroid/src/main/java/com/facebook/react/views/textinput/ReactEditText.java
+++ b/node_modules/react-native/ReactAndroid/src/main/java/com/facebook/react/views/textinput/ReactEditText.java
@@ -370,9 +370,12 @@ public class ReactEditText extends AppCompatEditText {
 
         if (clipData.getDescription().hasMimeType(ClipDescription.MIMETYPE_TEXT_PLAIN)) {
           type = ClipDescription.MIMETYPE_TEXT_PLAIN;
-          data = item.getText().toString();
-          if (mPasteWatcher != null) {
-            mPasteWatcher.onPaste(type, data);
+          CharSequence text = item.getText();
+          if (text != null) {
+            data = text.toString();
+            if (mPasteWatcher != null) {
+              mPasteWatcher.onPaste(type, data);
+            }
           }
           // Don't return - let the system proceed with default text pasting behavior
         }
