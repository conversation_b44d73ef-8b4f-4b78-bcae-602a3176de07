diff --git a/node_modules/react-native/React/Fabric/Mounting/ComponentViews/TextInput/RCTTextInputComponentView.mm b/node_modules/react-native/React/Fabric/Mounting/ComponentViews/TextInput/RCTTextInputComponentView.mm
index fef9c63..1859b31 100644
--- a/node_modules/react-native/React/Fabric/Mounting/ComponentViews/TextInput/RCTTextInputComponentView.mm
+++ b/node_modules/react-native/React/Fabric/Mounting/ComponentViews/TextInput/RCTTextInputComponentView.mm
@@ -70,6 +70,7 @@ @implementation RCTTextInputComponentView {
   NSDictionary<NSAttributedStringKey, id> *_originalTypingAttributes;
 
   BOOL _hasInputAccessoryView;
+  CGSize _previousContentSize;
 }
 
 #pragma mark - UIView overrides
@@ -86,6 +87,7 @@ - (instancetype)initWithFrame:(CGRect)frame
     _comingFromJS = NO;
     _didMoveToWindow = NO;
     _originalTypingAttributes = [_backedTextInputView.typingAttributes copy];
+    _previousContentSize = CGSizeZero;
 
     [self addSubview:_backedTextInputView];
     [self initializeReturnKeyType];
@@ -326,7 +328,6 @@ - (void)updateState:(const State::Shared &)state oldState:(const State::Shared &
 - (void)updateLayoutMetrics:(const LayoutMetrics &)layoutMetrics
            oldLayoutMetrics:(const LayoutMetrics &)oldLayoutMetrics
 {
-  CGSize previousContentSize = _backedTextInputView.contentSize;
 
   [super updateLayoutMetrics:layoutMetrics oldLayoutMetrics:oldLayoutMetrics];
 
@@ -335,7 +336,8 @@ - (void)updateLayoutMetrics:(const LayoutMetrics &)layoutMetrics
   _backedTextInputView.textContainerInset =
       RCTUIEdgeInsetsFromEdgeInsets(layoutMetrics.contentInsets - layoutMetrics.borderWidth);
 
-  if (!CGSizeEqualToSize(previousContentSize, _backedTextInputView.contentSize) && _eventEmitter) {
+  if (!CGSizeEqualToSize(_previousContentSize, _backedTextInputView.contentSize) && _eventEmitter) {
+    _previousContentSize = _backedTextInputView.contentSize;
     static_cast<const TextInputEventEmitter &>(*_eventEmitter).onContentSizeChange([self _textInputMetrics]);
   }
 }
