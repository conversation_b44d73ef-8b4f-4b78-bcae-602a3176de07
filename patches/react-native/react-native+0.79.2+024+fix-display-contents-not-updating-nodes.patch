diff --git a/node_modules/react-native/ReactCommon/yoga/yoga/algorithm/CalculateLayout.cpp b/node_modules/react-native/ReactCommon/yoga/yoga/algorithm/CalculateLayout.cpp
index 3618474..ab5b828 100644
--- a/node_modules/react-native/ReactCommon/yoga/yoga/algorithm/CalculateLayout.cpp
+++ b/node_modules/react-native/ReactCommon/yoga/yoga/algorithm/CalculateLayout.cpp
@@ -476,16 +476,19 @@ static void zeroOutLayoutRecursively(yoga::Node* const node) {
 }
 
 static void cleanupContentsNodesRecursively(yoga::Node* const node) {
-  for (auto child : node->getChildren()) {
-    if (child->style().display() == Display::Contents) {
-      child->getLayout() = {};
-      child->setLayoutDimension(0, Dimension::Width);
-      child->setLayoutDimension(0, Dimension::Height);
-      child->setHasNewLayout(true);
-      child->setDirty(false);
-      child->cloneChildrenIfNeeded();
-
-      cleanupContentsNodesRecursively(child);
+  if (node->hasContentsChildren()) [[unlikely]] {
+    node->cloneContentsChildrenIfNeeded();
+    for (auto child : node->getChildren()) {
+      if (child->style().display() == Display::Contents) {
+        child->getLayout() = {};
+        child->setLayoutDimension(0, Dimension::Width);
+        child->setLayoutDimension(0, Dimension::Height);
+        child->setHasNewLayout(true);
+        child->setDirty(false);
+        child->cloneChildrenIfNeeded();
+        
+        cleanupContentsNodesRecursively(child);
+      }
     }
   }
 }
diff --git a/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.cpp b/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.cpp
index dce42fb..f1bc5e8 100644
--- a/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.cpp
+++ b/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.cpp
@@ -181,6 +181,17 @@ void Node::setDirty(bool isDirty) {
   }
 }
 
+void Node::setChildren(const std::vector<Node*>& children) {
+  children_ = children;
+
+  contentsChildrenCount_ = 0;
+  for (const auto& child : children) {
+    if (child->style().display() == Display::Contents) {
+      contentsChildrenCount_++;
+    }
+  }
+}
+
 bool Node::removeChild(Node* child) {
   auto p = std::find(children_.begin(), children_.end(), child);
   if (p != children_.end()) {
@@ -379,6 +390,23 @@ void Node::cloneChildrenIfNeeded() {
     if (child->getOwner() != this) {
       child = resolveRef(config_->cloneNode(child, this, i));
       child->setOwner(this);
+      
+      if (child->hasContentsChildren()) [[unlikely]] {
+        child->cloneContentsChildrenIfNeeded();
+      }
+    }
+    i += 1;
+  }
+}
+
+void Node::cloneContentsChildrenIfNeeded() {
+  size_t i = 0;
+  for (Node*& child : children_) {
+    if (child->style().display() == Display::Contents &&
+        child->getOwner() != this) {
+      child = resolveRef(config_->cloneNode(child, this, i));
+      child->setOwner(this);
+      child->cloneChildrenIfNeeded();
     }
     i += 1;
   }
diff --git a/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.h b/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.h
index 5ae7d43..3454d54 100644
--- a/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.h
+++ b/node_modules/react-native/ReactCommon/yoga/yoga/node/Node.h
@@ -96,6 +96,10 @@ class YG_EXPORT Node : public ::YGNode {
     return config_->hasErrata(errata);
   }
   
+  bool hasContentsChildren() const {
+    return contentsChildrenCount_ != 0;
+  }
+
   YGDirtiedFunc getDirtiedFunc() const {
     return dirtiedFunc_;
   }
@@ -244,15 +248,12 @@ class YG_EXPORT Node : public ::YGNode {
     owner_ = owner;
   }
 
-  void setChildren(const std::vector<Node*>& children) {
-    children_ = children;
-  }
-
   // TODO: rvalue override for setChildren
 
   void setConfig(Config* config);
 
   void setDirty(bool isDirty);
+  void setChildren(const std::vector<Node*>& children);
   void setLayoutLastOwnerDirection(Direction direction);
   void setLayoutComputedFlexBasis(FloatOptional computedFlexBasis);
   void setLayoutComputedFlexBasisGeneration(
@@ -286,6 +287,7 @@ class YG_EXPORT Node : public ::YGNode {
   void removeChild(size_t index);
 
   void cloneChildrenIfNeeded();
+  void cloneContentsChildrenIfNeeded();
   void markDirtyAndPropagate();
   float resolveFlexGrow() const;
   float resolveFlexShrink() const;
