---
title: Connect personal bank account
description: Receive reimbursements for expense reports submitted to your employer
keyword: [Expensify Classic, deposit account, reimbursement account]
---

<div id="expensify-classic" markdown="1">

Employees can connect a personal deposit-only bank account to receive reimbursements for their expense reports.

---

# Connect a Deposit-only Bank Account

1. Hover over **Settings**, then click **Account**.
2. Click the **Wallet** tab on the left.
3. Under **Bank Accounts**, click **Add Deposit-Only Bank Account**, then click **Connect to your bank**.
4. Click **Continue**.
5. Search for your bank account in the list of banks
6. Follow the prompts to sign in to your bank account using the corresponding username and password.
   - If your bank doesn’t appear, click the **X** in the right corner of the Plaid pop-up window, then click **Connect Manually**. You’ll then manually enter your account information and click **Save & Continue**.
7. Enter the name, address, and phone number associated with the account
8. Click **Save & Continue**.


Once the bank account is connected to your Expensify account, future reimbursements will be deposited directly.

## Using Global Reimbursement

If your organization has global reimbursement enabled and you want to add a bank account outside the US, follow the steps above.

After clicking **Add Deposit-Only Bank Account**, under **Settings > Accounts > Wallet**, click **Switch Country** at the top of the bank selection screen. This will allow you to add a deposit account from a supported country and receive reimbursements in your local currency.

---

# FAQ

## I connected my deposit-only bank account. Why haven’t I received my reimbursement?

Check the following possible issues:
- The estimated deposit date on the report has not arrived yet.
- The bank account information is incorrect. If you believe you may have entered the wrong account, contact **Concierge (Expensify’s support team)** and provide the **Report ID** for the missing reimbursement.
- Your bank account isn’t set up for **Direct Deposit/ACH** — please contact your bank to confirm.

## What happens if my bank requires an additional security check before adding it to a third party?

If your bank account has **two-factor authentication (2FA)** or another security step enabled, you should be prompted to complete this authentication step when connecting the account to Expensify.

However, if you encounter an error during this process, you can close the pop-up window and select **Connect Manually** to add the account manually.

## Why don’t the account number and routing number in Expensify match what’s on my bank statement?

Some banks, such as Chase, may display masked or tokenized account numbers when connected via Plaid. This is a security feature implemented by the bank and does not indicate any setup error. As long as the connection was successful and you see your account listed in your **Wallet**, reimbursements should process correctly.

## Why can't I add my HSBC bank account details in Singapore?

You can. HSBC typically includes the **branch code** which is the final three digits of the Swift Code (HSBCSGS2XXX) in their "Account Number". If you see figures like "************", please drop first 3 digit (146) which is the Branch Code.

</div>
