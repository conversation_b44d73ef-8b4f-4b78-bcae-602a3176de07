name: Check unused styles

on:
  workflow_call:
  pull_request:
    types: [opened, synchronize]
    branches-ignore: [staging, production]
    paths: ['src/**', '.github/workflows/unused-styles.yml', 'scripts/findUnusedStyles.ts']

concurrency:
  group: ${{ github.ref == 'refs/heads/main' && format('{0}-{1}', github.ref, github.sha) || github.ref }}-unused-styles
  cancel-in-progress: true

jobs:
  check-unused-styles:
    name: Check for unused styles
    if: ${{ github.event.head_commit.author.name != 'OSBotify' || github.event_name == 'workflow_call' }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        # v4
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608

      - name: Setup Node
        uses: ./.github/actions/composite/setupNode

      - name: Run unused style searcher
        run: npx ts-node scripts/findUnusedStyles.ts
        